import { createSlice } from "@reduxjs/toolkit";
import { githubFile, RepoData, commit } from "../lib/type";
interface RepoState {
  folderstructure: githubFile['folderstructure'][];
  repoData: RepoData[];
  commit: commit[];
}
export const repoSlice : RepoState = createSlice({
  name: "repo",
  initialState: {
    folderstructure: [
        {
            path: "",
            mode: "",
            type: "",
            size: 0,
            sha: "",
            url: "",
        }
    ],
    repoData:[
        {
            name: "",
            description: "",
            language: "",
            stars: 0,
            forks: 0,
            url: "",
            created_at: "",
            updated_at: "",
        }
    ],
    commit:[
        {
            commit:{
                author:{
                    name: "",
                    email: "",
                    date: "",
                },
                message: "",
                tree:{
                    sha: "",
                    url: "",
                },
                comment_count: 0,
            }
        }
    ]
  },
  reducers: {
    setFolderStructure: (state, action) => {
      state.folderstructure = action.payload;
    },
    setRepoData: (state, action) => {
      state.repoData = action.payload;
    },
    setCommit: (state, action) => {
      state.commit = action.payload;
    },
  },
});

export const { setRepoData, setFolderStructure, setCommit } = repoSlice.actions;
export default repoSlice.reducer;

{"$schema": "https://json.schemastore.org/tsconfig", "extends": "./base.json", "compilerOptions": {"plugins": [{"name": "next"}], "target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "incremental": true, "esModuleInterop": true, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve"}, "include": ["src", "next-env.d.ts"], "exclude": ["node_modules"]}
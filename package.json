{"name": "with-docker", "version": "0.0.0", "private": true, "scripts": {"build": "turbo run build", "clean": "turbo run clean", "dev": "turbo run dev", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "lint": "turbo run lint", "test": "turbo run test"}, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "next-auth": "^4.24.11"}, "devDependencies": {"prettier": "^3.2.5", "turbo": "^2.5.4"}, "engines": {"node": ">=14.0.0", "npm": ">=7.0.0"}, "packageManager": "pnpm@10.12.1"}